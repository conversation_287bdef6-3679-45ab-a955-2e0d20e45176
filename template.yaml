AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'Multi-Metric Step Scaling for Existing ECS Service - Data-Intensive Application'

Parameters:
  # Existing Service Parameters
  ExistingClusterName:
    Type: String
    Description: Name of the existing ECS cluster
    MinLength: 1
  
  ExistingServiceName:
    Type: String
    Description: Name of the existing ECS service
    MinLength: 1
  
  LoadBalancerFullName:
    Type: String
    Description: Full name of the Application Load Balancer (app/name/id)
    MinLength: 1
  
  TargetGroupFullName:
    Type: String
    Description: Full name of the Target Group
    MinLength: 1
  
  # Scaling Capacity Parameters
  MinCapacity:
    Type: Number
    Default: 3
    MinValue: 1
    MaxValue: 100
    Description: Minimum number of tasks (higher for data processing stability)
  
  MaxCapacity:
    Type: Number
    Default: 15
    MinValue: 2
    MaxValue: 1000
    Description: Maximum number of tasks (conservative for cost control)
  
  # CPU Scaling Parameters
  CPUScaleUpThreshold:
    Type: Number
    Default: 55
    MinValue: 10
    MaxValue: 90
    Description: CPU threshold for scaling up (proactive)
  
  CPUScaleDownThreshold:
    Type: Number
    Default: 35
    MinValue: 5
    MaxValue: 80
    Description: CPU threshold for scaling down (conservative)
  
  # Memory Scaling Parameters
  MemoryScaleUpThreshold:
    Type: Number
    Default: 55
    MinValue: 10
    MaxValue: 90
    Description: Memory threshold for scaling up (proactive)
  
  MemoryScaleDownThreshold:
    Type: Number
    Default: 35
    MinValue: 5
    MaxValue: 80
    Description: Memory threshold for scaling down (conservative)
  
  # ALB Request Count Parameters
  BaseRPMPerTask:
    Type: Number
    Default: 4750
    MinValue: 50
    MaxValue: 10000
    Description: Base requests per minute per task for scaling calculations
  
  HighRequestCountMultiplier:
    Type: Number
    Default: 1.0
    MinValue: 0.5
    MaxValue: 3.0
    Description: Multiplier for high request count threshold (1.0 = 300 RPM per task)
  
  LowRequestCountMultiplier:
    Type: Number
    Default: 0.17
    MinValue: 0.05
    MaxValue: 0.5
    Description: Multiplier for low request count threshold (0.17 = ~50 RPM per task)
  
  # Cooldown Parameters
  CPUMemoryScaleUpCooldown:
    Type: Number
    Default: 60
    MinValue: 60
    MaxValue: 3600
    Description: Scale-up cooldown for CPU/Memory in seconds (1 minute)
  
  ALBScaleUpCooldown:
    Type: Number
    Default: 60
    MinValue: 60
    MaxValue: 1800
    Description: Scale-up cooldown for ALB requests in seconds (1 minute)
  
  ScaleDownCooldown:
    Type: Number
    Default: 900
    MinValue: 300
    MaxValue: 3600
    Description: Scale-down cooldown in seconds (15 minutes)
  
  # Notification Parameters
  NotificationEmail:
    Type: String
    Default: ""
    Description: Email address for scaling notifications (optional)
  
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name for resource tagging
  
  CreateScalableTarget:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Whether to create a new ScalableTarget (set to false if one already exists)
  
  ExistingScalableTargetId:
    Type: String
    Default: ""
    Description: ARN of existing ScalableTarget (required when CreateScalableTarget is false)

Conditions:
  HasNotificationEmail: !Not [!Equals [!Ref NotificationEmail, ""]]
  ShouldCreateScalableTarget: !Equals [!Ref CreateScalableTarget, "true"]

Resources:
  # SNS Topic for Scaling Notifications
  ScalingNotificationTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub "${ExistingServiceName}-scaling-notifications"
      DisplayName: !Sub "ECS Scaling Notifications for ${ExistingServiceName}"
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Service
          Value: !Ref ExistingServiceName

  ScalingNotificationSubscriptionV2:
    Type: AWS::SNS::Subscription
    Condition: HasNotificationEmail
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
    Properties:
      TopicArn: !Ref ScalingNotificationTopic
      Protocol: email
      Endpoint: !Ref NotificationEmail
      # Force recreation of subscription to trigger new email confirmation
      # Updated: 2025-07-27 for prod email approval issue - Changed resource name to force recreation
      DeliveryPolicy:
        healthyRetryPolicy:
          numRetries: 3
          minDelayTarget: 20
          maxDelayTarget: 20

  # Application Auto Scaling Target
  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Condition: ShouldCreateScalableTarget
    Properties:
      ServiceNamespace: ecs
      ResourceId: !Sub "service/${ExistingClusterName}/${ExistingServiceName}"
      ScalableDimension: ecs:service:DesiredCount
      MinCapacity: !Ref MinCapacity
      MaxCapacity: !Ref MaxCapacity
      RoleARN: !Sub "arn:aws:iam::${AWS::AccountId}:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService"

  # Memory-Based Scale-Up Policy
  MemoryScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub "${ExistingServiceName}-MemoryScaleUpPolicy"
      PolicyType: StepScaling
      ScalingTargetId: !If [ShouldCreateScalableTarget, !Ref ScalableTarget, !Ref ExistingScalableTargetId]
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: !Ref CPUMemoryScaleUpCooldown
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            MetricIntervalUpperBound: 10
            ScalingAdjustment: 5    # 55-65% memory: +5 tasks
          - MetricIntervalLowerBound: 10
            MetricIntervalUpperBound: 20
            ScalingAdjustment: 10   # 65-75% memory: +10 tasks
          - MetricIntervalLowerBound: 20
            ScalingAdjustment: 15   # 75%+ memory: +15 tasks

  # CPU-Based Scale-Up Policy
  CPUScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub "${ExistingServiceName}-CPUScaleUpPolicy"
      PolicyType: StepScaling
      ScalingTargetId: !If [ShouldCreateScalableTarget, !Ref ScalableTarget, !Ref ExistingScalableTargetId]
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: !Ref CPUMemoryScaleUpCooldown
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            MetricIntervalUpperBound: 10
            ScalingAdjustment: 5    # 55-65% CPU: +5 tasks
          - MetricIntervalLowerBound: 10
            MetricIntervalUpperBound: 15
            ScalingAdjustment: 10   # 65-70% CPU: +10 tasks
          - MetricIntervalLowerBound: 15
            ScalingAdjustment: 15   # 70%+ CPU: +15 tasks

  # ALB Request Count Scale-Up Policy
  ALBRequestScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub "${ExistingServiceName}-ALBRequestScaleUpPolicy"
      PolicyType: StepScaling
      ScalingTargetId: !If [ShouldCreateScalableTarget, !Ref ScalableTarget, !Ref ExistingScalableTargetId]
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: !Ref ALBScaleUpCooldown
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            MetricIntervalUpperBound: 2810
            ScalingAdjustment: 5    # 1190-4000 requests: +5 tasks
          - MetricIntervalLowerBound: 2810
            MetricIntervalUpperBound: 4810
            ScalingAdjustment: 10   # 4000-6000 requests: +10 tasks
          - MetricIntervalLowerBound: 4810
            MetricIntervalUpperBound: 8810
            ScalingAdjustment: 15   # 6000-10000 requests: +15 tasks
          - MetricIntervalLowerBound: 8810
            ScalingAdjustment: 20   # 10000+ requests: +20 tasks

  # Conservative Scale-Down Policy
  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub "${ExistingServiceName}-ConservativeScaleDownPolicy"
      PolicyType: StepScaling
      ScalingTargetId: !If [ShouldCreateScalableTarget, !Ref ScalableTarget, !Ref ExistingScalableTargetId]
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: !Ref ScaleDownCooldown
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalUpperBound: -10
            MetricIntervalLowerBound: -20
            ScalingAdjustment: -1   # 25-35% utilization: -1 task
          - MetricIntervalUpperBound: -20
            MetricIntervalLowerBound: -30
            ScalingAdjustment: -1   # 15-25% utilization: -1 task
          - MetricIntervalUpperBound: -30
            ScalingAdjustment: -2   # <15% utilization: -2 tasks

  # Emergency Scale-Up Policy for Critical Situations
  EmergencyScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub "${ExistingServiceName}-EmergencyScaleUpPolicy"
      PolicyType: StepScaling
      ScalingTargetId: !If [ShouldCreateScalableTarget, !Ref ScalableTarget, !Ref ExistingScalableTargetId]
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: 180  # Shorter cooldown for emergency situations
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            ScalingAdjustment: 5    # Emergency: +5 tasks

  # CloudWatch Alarms - Memory Utilization
  HighMemoryAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-HighMemoryUtilization"
      AlarmDescription: "Memory utilization is high for data processing service"
      MetricName: MemoryUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 60
      EvaluationPeriods: 2
      Threshold: !Ref MemoryScaleUpThreshold
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
      AlarmActions:
        - !Ref MemoryScaleUpPolicy
        - !Ref ScalingNotificationTopic

  # Task Count Alarm - Monitors current running task count
  TaskCountAboveMinimumAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-TaskCountAboveMinimum"
      AlarmDescription: "Current task count is above minimum - safe to scale down"
      MetricName: RunningTaskCount
      Namespace: AWS/ECS
      Statistic: Average
      Period: 60
      EvaluationPeriods: 1
      Threshold: !Ref MinCapacity
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
      TreatMissingData: breaching

  LowMemoryAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-LowMemoryUtilization"
      AlarmDescription: "Memory utilization is low - safe to scale down"
      MetricName: MemoryUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 4
      Threshold: !Ref MemoryScaleDownThreshold
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
      TreatMissingData: notBreaching

  # CloudWatch Alarms - CPU Utilization
  HighCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-HighCPUUtilization"
      AlarmDescription: "CPU utilization is high for data processing service"
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 60
      EvaluationPeriods: 2
      Threshold: !Ref CPUScaleUpThreshold
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
      AlarmActions:
        - !Ref CPUScaleUpPolicy
        - !Ref ScalingNotificationTopic

  LowCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-LowCPUUtilization"
      AlarmDescription: "CPU utilization is low - safe to scale down"
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 4
      Threshold: !Ref CPUScaleDownThreshold
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
      TreatMissingData: notBreaching

  # CloudWatch Alarms - ALB Request Count (Initial static thresholds)
  HighRequestCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-HighRequestCount"
      AlarmDescription: "ALB request count is high - scale up needed"
      MetricName: RequestCount
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 2
      Threshold: !Ref BaseRPMPerTask  # Will be updated dynamically by Lambda
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: !Ref LoadBalancerFullName
      AlarmActions:
        - !Ref ALBRequestScaleUpPolicy
        - !Ref ScalingNotificationTopic

  LowRequestCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-LowRequestCount"
      AlarmDescription: "ALB request count is low - safe to scale down"
      MetricName: RequestCount
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 6
      Threshold: 5000  # 1000 requests per minute × 5-minute period
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: !Ref LoadBalancerFullName
      TreatMissingData: notBreaching

  # Composite Alarms for Intelligent Scaling
  CompositeScaleUpAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-CompositeScaleUp"
      AlarmDescription: "Scale up when CPU, Memory, or Request Count is high"
      AlarmRule: !Sub "ALARM(\"${HighCPUAlarm}\") OR ALARM(\"${HighMemoryAlarm}\") OR ALARM(\"${HighRequestCountAlarm}\")"
      ActionsEnabled: true
      AlarmActions:
        - !Ref ScalingNotificationTopic

  CompositeScaleDownAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-CompositeScaleDown"
      AlarmDescription: "Scale down only when CPU, Memory, AND Request Count are all low AND task count is above minimum"
      AlarmRule: !Sub "ALARM(\"${LowCPUAlarm}\") AND ALARM(\"${LowMemoryAlarm}\") AND ALARM(\"${LowRequestCountAlarm}\") AND ALARM(\"${TaskCountAboveMinimumAlarm}\")"
      ActionsEnabled: true
      AlarmActions:
        - !Ref ScalingNotificationTopic

  # Individual alarm using metric math to trigger scale down policy
  ScaleDownTriggerAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-ScaleDownTrigger"
      AlarmDescription: "Triggers scale down when all conditions are met: low CPU, low memory, low requests, and task count above minimum"
      ComparisonOperator: LessThanThreshold
      EvaluationPeriods: 1
      Threshold: 1
      TreatMissingData: notBreaching
      Metrics:
        - Id: m1
          ReturnData: false
          MetricStat:
            Metric:
              MetricName: CPUUtilization
              Namespace: AWS/ECS
              Dimensions:
                - Name: ServiceName
                  Value: !Ref ExistingServiceName
                - Name: ClusterName
                  Value: !Ref ExistingClusterName
            Period: 300
            Stat: Average
        - Id: m2
          ReturnData: false
          MetricStat:
            Metric:
              MetricName: MemoryUtilization
              Namespace: AWS/ECS
              Dimensions:
                - Name: ServiceName
                  Value: !Ref ExistingServiceName
                - Name: ClusterName
                  Value: !Ref ExistingClusterName
            Period: 300
            Stat: Average
        - Id: m3
          ReturnData: false
          MetricStat:
            Metric:
              MetricName: RequestCount
              Namespace: AWS/ApplicationELB
              Dimensions:
                - Name: LoadBalancer
                  Value: !Ref LoadBalancerFullName
            Period: 300
            Stat: Sum
        - Id: m4
          ReturnData: false
          MetricStat:
            Metric:
              MetricName: RunningTaskCount
              Namespace: AWS/ECS
              Dimensions:
                - Name: ServiceName
                  Value: !Ref ExistingServiceName
                - Name: ClusterName
                  Value: !Ref ExistingClusterName
            Period: 300
            Stat: Average
        - Id: e1
          ReturnData: true
          Expression: !Sub "IF(m1 < ${CPUScaleDownThreshold} AND m2 < ${MemoryScaleDownThreshold} AND m3 < 50 AND m4 > ${MinCapacity}, 0, 1)"
      AlarmActions:
        - !Ref ScaleDownPolicy

  EmergencyScaleUpAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName: !Sub "${ExistingServiceName}-EmergencyScaleUp"
      AlarmDescription: "Emergency scaling when multiple metrics are critically high"
      AlarmRule: !Sub "(ALARM(\"${HighCPUAlarm}\") AND ALARM(\"${HighMemoryAlarm}\")) OR (ALARM(\"${HighCPUAlarm}\") AND ALARM(\"${HighRequestCountAlarm}\")) OR (ALARM(\"${HighMemoryAlarm}\") AND ALARM(\"${HighRequestCountAlarm}\"))"
      ActionsEnabled: true
      AlarmActions:
        - !Ref ScalingNotificationTopic

  # IAM Role for Lambda Function
  RequestThresholdCalculatorRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ExistingServiceName}-RequestThresholdCalculatorRole"
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: ECSAndCloudWatchAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ecs:DescribeServices
                  - ecs:ListServices
                  - cloudwatch:PutMetricAlarm
                  - cloudwatch:DescribeAlarms
                Resource: '*'

  # Lambda Function for Dynamic Request Threshold Calculation
  RequestThresholdCalculator:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${ExistingServiceName}-RequestThresholdCalculator"
      CodeUri: src/
      Handler: threshold_calculator.lambda_handler
      Runtime: python3.9
      Timeout: 60
      MemorySize: 128
      Role: !GetAtt RequestThresholdCalculatorRole.Arn
      Environment:
        Variables:
          CLUSTER_NAME: !Ref ExistingClusterName
          SERVICE_NAME: !Ref ExistingServiceName
          BASE_RPM_PER_TASK: !Ref BaseRPMPerTask
          HIGH_REQUEST_MULTIPLIER: !Ref HighRequestCountMultiplier
          LOW_REQUEST_MULTIPLIER: !Ref LowRequestCountMultiplier
      # Events section removed to use fixed thresholds instead of dynamic calculation
      # The Lambda function is still available for manual execution if needed
      Tags:
        Environment: !Ref Environment
        Service: !Ref ExistingServiceName

  # CloudWatch Dashboard
  MultiMetricDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: !Sub "${ExistingServiceName}-MultiMetric-Scaling"
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  ["AWS/ECS", "CPUUtilization", "ServiceName", "${ExistingServiceName}", "ClusterName", "${ExistingClusterName}"],
                  [".", "MemoryUtilization", ".", ".", ".", "."],
                  ["AWS/ApplicationELB", "RequestCount", "LoadBalancer", "${LoadBalancerFullName}"]
                ],
                "period": 300,
                "stat": "Average",
                "region": "${AWS::Region}",
                "title": "Multi-Metric Scaling Overview",
                "yAxis": {
                  "left": {
                    "min": 0,
                    "max": 100
                  }
                }
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  ["ECS/ContainerInsights", "RunningTaskCount", "ServiceName", "${ExistingServiceName}", "ClusterName", "${ExistingClusterName}"],
                  ["ECS/ContainerInsights", "DeploymentCount", "ServiceName", "${ExistingServiceName}", "ClusterName", "${ExistingClusterName}"]
                ],
                "period": 300,
                "stat": "Average",
                "region": "${AWS::Region}",
                "title": "Task Count and Scaling Activities"
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 6,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  ["AWS/ApplicationELB", "TargetResponseTime", "LoadBalancer", "${LoadBalancerFullName}"],
                  [".", "HTTPCode_Target_2XX_Count", ".", "."],
                  [".", "HTTPCode_Target_5XX_Count", ".", "."]
                ],
                "period": 300,
                "stat": "Average",
                "region": "${AWS::Region}",
                "title": "ALB Performance Metrics"
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 6,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  ["AWS/Logs", "IncomingLogEvents", "LogGroupName", "/ecs/api-production"]
                ],
                "period": 300,
                "stat": "Sum",
                "region": "${AWS::Region}",
                "title": "Application Log Activity"
              }
            }
          ]
        }

Outputs:
  ScalableTargetId:
    Description: "Application Auto Scaling Target ID"
    Value: !If [ShouldCreateScalableTarget, !Ref ScalableTarget, !Ref ExistingScalableTargetId]
    Export:
      Name: !Sub "${AWS::StackName}-ScalableTargetId"

  ScalingNotificationTopicArn:
    Description: "SNS Topic ARN for scaling notifications"
    Value: !Ref ScalingNotificationTopic
    Export:
      Name: !Sub "${AWS::StackName}-ScalingNotificationTopicArn"

  DashboardURL:
    Description: "CloudWatch Dashboard URL"
    Value: !Sub "https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${ExistingServiceName}-MultiMetric-Scaling"

  RequestThresholdCalculatorFunctionName:
    Description: "Lambda function name for request threshold calculation"
    Value: !Ref RequestThresholdCalculator
    Export:
      Name: !Sub "${AWS::StackName}-RequestThresholdCalculatorFunctionName"

  CompositeAlarms:
    Description: "Composite alarm names for monitoring"
    Value: !Sub "${CompositeScaleUpAlarm},${CompositeScaleDownAlarm},${EmergencyScaleUpAlarm}"